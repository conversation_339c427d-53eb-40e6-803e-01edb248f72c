import { Provide, Inject, ILogger } from '@midwayjs/core';
import {
  Order,
  OrderDetail,
  Service,
  OrderAmountAnomalyRecord,
  OrderAmountFixLog,
  OrderAmountAnomalyType,
  AnomalyProcessStatus,
  FixOperationType,
  FixResult,
} from '../entity';
import { Op, literal, Transaction } from 'sequelize';
import { OrderStatus } from '../common/Constant';

/**
 * 订单金额异常检查服务
 * 负责检测、修复和管理订单金额异常
 */
@Provide()
export class OrderAmountAnomalyService {
  @Inject()
  logger: ILogger;

  /**
   * 检查订单金额异常
   * @param options 检查选项
   */
  async checkOrderAmountAnomalies(
    options: {
      threshold?: number; // 异常阈值百分比，默认1%，设为0则检测任何不一致
      absoluteThreshold?: number; // 绝对金额阈值（元），优先级高于百分比阈值
      limit?: number; // 返回记录数限制
      orderId?: number; // 指定订单ID
      skipExisting?: boolean; // 是否跳过已记录的异常
    } = {}
  ) {
    try {
      const {
        threshold = 1,
        absoluteThreshold,
        limit = 100,
        orderId,
        skipExisting = true,
      } = options;

      // 构建查询条件
      const whereConditions: any = {
        [Op.and]: [
          { originalPrice: { [Op.gt]: 0 } },
          { totalFee: { [Op.gte]: 0 } },
          // 排除已取消、退款中、已退款的订单
          { status: { [Op.notIn]: ['已取消', '退款中', '已退款'] } },
        ],
      };

      if (orderId) {
        whereConditions.id = orderId;
      }

      // 查找可能存在金额异常的订单
      const orders = await Order.findAll({
        where: whereConditions,
        include: [
          {
            model: OrderDetail,
            include: [
              {
                model: Service,
                attributes: ['id', 'serviceName', 'basePrice'],
                required: false,
              },
            ],
          },
        ],
        limit: orderId ? 1 : limit,
        order: [['createdAt', 'DESC']],
      });

      const anomalies: any[] = [];
      const existingAnomalyOrderIds = new Set<number>();

      // 如果需要跳过已存在的异常记录，先查询已记录的订单ID
      if (skipExisting) {
        const existingRecords = await OrderAmountAnomalyRecord.findAll({
          where: {
            processStatus: {
              [Op.notIn]: [
                AnomalyProcessStatus.AUTO_FIXED,
                AnomalyProcessStatus.MANUAL_FIXED,
              ],
            },
          },
          attributes: ['orderId'],
        });
        existingRecords.forEach(record =>
          existingAnomalyOrderIds.add(record.orderId)
        );
      }

      for (const order of orders) {
        // 跳过已记录的异常
        if (skipExisting && existingAnomalyOrderIds.has(order.id)) {
          continue;
        }

        const anomaly = await this.analyzeOrderAmountAnomaly(order, threshold, absoluteThreshold);
        if (anomaly) {
          anomalies.push(anomaly);
        }
      }

      this.logger.info(`订单金额异常检查完成，发现${anomalies.length}个异常`);

      return {
        anomalyCount: anomalies.length,
        threshold,
        anomalies,
      };
    } catch (error) {
      this.logger.error('检查订单金额异常失败:', error);
      throw error;
    }
  }

  /**
   * 分析单个订单的金额异常
   * @param order 订单对象
   * @param threshold 异常阈值百分比
   * @param absoluteThreshold 绝对金额阈值
   */
  private async analyzeOrderAmountAnomaly(
    order: Order,
    threshold: number,
    absoluteThreshold?: number
  ) {
    try {
      // 计算订单应有的原价
      const calculatedOriginalPrice = this.calculateOrderOriginalPrice(
        order.orderDetails || []
      );

      // 计算预期实付金额
      const expectedTotalFee =
        calculatedOriginalPrice -
        (order.cardDeduction || 0) -
        (order.couponDeduction || 0);

      // 检查各种异常情况
      const anomalies: any[] = [];

      // 1. 检查原价是否缺失
      if (!order.originalPrice || order.originalPrice <= 0) {
        anomalies.push({
          type: OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE,
          description: '订单原价缺失或为0',
          severity: 3,
          canAutoFix: calculatedOriginalPrice > 0,
          calculatedOriginalPrice,
          anomalyAmount: Math.abs(
            calculatedOriginalPrice - (order.originalPrice || 0)
          ),
        });
      }

      // 2. 检查原价与计算价格的差异
      if (order.originalPrice > 0 && calculatedOriginalPrice > 0) {
        const priceDifference = Math.abs(
          order.originalPrice - calculatedOriginalPrice
        );
        const differenceRate =
          (priceDifference / calculatedOriginalPrice) * 100;

        // 判断是否超过阈值
        let isAnomalous = false;
        let description = '';

        if (absoluteThreshold !== undefined) {
          // 使用绝对金额阈值
          isAnomalous = priceDifference > absoluteThreshold;
          description = `订单原价与计算价格不匹配，差异${priceDifference.toFixed(2)}元`;
        } else if (threshold === 0) {
          // 阈值为0时，检测任何不一致
          isAnomalous = priceDifference > 0;
          description = `订单原价与计算价格不匹配，差异${priceDifference.toFixed(2)}元（${differenceRate.toFixed(2)}%）`;
        } else {
          // 使用百分比阈值
          isAnomalous = differenceRate > threshold;
          description = `订单原价与计算价格不匹配，差异${differenceRate.toFixed(2)}%`;
        }

        if (isAnomalous) {
          anomalies.push({
            type: OrderAmountAnomalyType.PRICE_MISMATCH,
            description,
            severity: this.calculateSeverity(differenceRate),
            canAutoFix: this.canAutoFixPriceMismatch(
              order,
              calculatedOriginalPrice
            ),
            calculatedOriginalPrice,
            anomalyAmount: priceDifference,
          });
        }
      }

      // 3. 检查实付金额异常
      if (order.originalPrice > 0) {
        const totalFeeDifference = Math.abs(order.totalFee - expectedTotalFee);
        const totalFeeRate =
          order.originalPrice > 0
            ? (totalFeeDifference / order.originalPrice) * 100
            : 0;

        // 判断实付金额是否异常
        let isTotalFeeAnomalous = false;
        let totalFeeDescription = '';

        if (absoluteThreshold !== undefined) {
          // 使用绝对金额阈值
          isTotalFeeAnomalous = totalFeeDifference > absoluteThreshold;
          totalFeeDescription = `实付金额计算异常，差异${totalFeeDifference.toFixed(2)}元`;
        } else if (threshold === 0) {
          // 阈值为0时，检测任何不一致
          isTotalFeeAnomalous = totalFeeDifference > 0;
          totalFeeDescription = `实付金额计算异常，差异${totalFeeDifference.toFixed(2)}元（${totalFeeRate.toFixed(2)}%）`;
        } else {
          // 使用百分比阈值
          isTotalFeeAnomalous = totalFeeRate > threshold;
          totalFeeDescription = `实付金额计算异常，差异${totalFeeRate.toFixed(2)}%`;
        }

        if (isTotalFeeAnomalous) {
          anomalies.push({
            type: OrderAmountAnomalyType.CALCULATION_ERROR,
            description: totalFeeDescription,
            severity: this.calculateSeverity(totalFeeRate),
            canAutoFix: this.canAutoFixCalculationError(order),
            calculatedOriginalPrice,
            anomalyAmount: totalFeeDifference,
          });
        }
      }

      // 4. 检查优惠金额异常
      const totalDiscount =
        (order.cardDeduction || 0) + (order.couponDeduction || 0);
      if (totalDiscount > order.originalPrice) {
        anomalies.push({
          type: OrderAmountAnomalyType.DISCOUNT_ANOMALY,
          description: `优惠金额超过订单原价`,
          severity: 4,
          canAutoFix: false,
          calculatedOriginalPrice,
          anomalyAmount: totalDiscount - order.originalPrice,
        });
      }

      // 如果发现异常，返回最严重的一个
      if (anomalies.length > 0) {
        const mostSevereAnomaly = anomalies.reduce((prev, current) =>
          current.severity > prev.severity ? current : prev
        );

        return {
          orderId: order.id,
          orderSn: order.sn,
          anomalyType: mostSevereAnomaly.type,
          description: mostSevereAnomaly.description,
          severity: mostSevereAnomaly.severity,
          canAutoFix: mostSevereAnomaly.canAutoFix,
          calculatedOriginalPrice: mostSevereAnomaly.calculatedOriginalPrice,
          anomalyAmount: mostSevereAnomaly.anomalyAmount,
          currentData: {
            originalPrice: order.originalPrice,
            totalFee: order.totalFee,
            cardDeduction: order.cardDeduction,
            couponDeduction: order.couponDeduction,
          },
          allAnomalies: anomalies,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(`分析订单${order.sn}金额异常失败:`, error);
      return null;
    }
  }

  /**
   * 计算订单原价
   * @param orderDetails 订单详情列表
   */
  private calculateOrderOriginalPrice(orderDetails: OrderDetail[]): number {
    let totalPrice = 0;

    for (const detail of orderDetails) {
      let priceToAdd = 0;

      if (detail.servicePrice && detail.servicePrice > 0) {
        priceToAdd = Number(detail.servicePrice);
      } else if (detail.service && detail.service.basePrice) {
        priceToAdd = Number(detail.service.basePrice);
      }

      if (!isNaN(priceToAdd) && isFinite(priceToAdd)) {
        totalPrice += priceToAdd;
      }
    }

    return totalPrice;
  }

  /**
   * 计算异常严重程度
   * @param differenceRate 差异百分比
   */
  private calculateSeverity(differenceRate: number): number {
    if (differenceRate >= 50) return 5;
    if (differenceRate >= 20) return 4;
    if (differenceRate >= 10) return 3;
    if (differenceRate >= 5) return 2;
    return 1;
  }

  /**
   * 判断价格不匹配是否可以自动修复
   */
  private canAutoFixPriceMismatch(
    order: Order,
    calculatedPrice: number
  ): boolean {
    // 如果计算出的价格合理且订单状态允许修改，则可以自动修复
    return (
      calculatedPrice > 0 &&
      ['待付款', '已付款', '已确认'].includes(order.status)
    );
  }

  /**
   * 判断计算错误是否可以自动修复
   */
  private canAutoFixCalculationError(order: Order): boolean {
    // 如果订单状态允许修改，则可以自动修复
    return ['待付款', '已付款', '已确认'].includes(order.status);
  }

  /**
   * 创建异常记录
   * @param anomalyData 异常数据
   */
  async createAnomalyRecord(anomalyData: any) {
    try {
      const record = await OrderAmountAnomalyRecord.create({
        orderId: anomalyData.orderId,
        orderSn: anomalyData.orderSn,
        anomalyType: anomalyData.anomalyType,
        processStatus: AnomalyProcessStatus.PENDING,
        description: anomalyData.description,
        anomalyDetails: JSON.stringify(anomalyData),
        originalPrice: anomalyData.currentData.originalPrice || 0,
        totalFee: anomalyData.currentData.totalFee || 0,
        cardDeduction: anomalyData.currentData.cardDeduction || 0,
        couponDeduction: anomalyData.currentData.couponDeduction || 0,
        calculatedOriginalPrice: anomalyData.calculatedOriginalPrice,
        anomalyAmount: anomalyData.anomalyAmount,
        severity: anomalyData.severity,
        canAutoFix: anomalyData.canAutoFix,
        autoFixAttempts: 0,
        fixSuggestion: this.generateFixSuggestion(anomalyData),
        isReverted: false,
      });

      this.logger.info(`创建异常记录成功，记录ID: ${record.id}`);
      return record;
    } catch (error) {
      this.logger.error('创建异常记录失败:', error);
      throw error;
    }
  }

  /**
   * 生成修复建议
   */
  private generateFixSuggestion(anomalyData: any): string {
    switch (anomalyData.anomalyType) {
      case OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE:
        return `建议将原价设置为计算值: ${anomalyData.calculatedOriginalPrice}元`;
      case OrderAmountAnomalyType.PRICE_MISMATCH:
        return `建议将原价从${anomalyData.currentData.originalPrice}元调整为${anomalyData.calculatedOriginalPrice}元`;
      case OrderAmountAnomalyType.CALCULATION_ERROR:
        return `建议重新计算实付金额，当前${anomalyData.currentData.totalFee}元可能有误`;
      case OrderAmountAnomalyType.DISCOUNT_ANOMALY:
        return `优惠金额异常，需要人工检查优惠券和权益卡使用情况`;
      default:
        return '需要人工检查和处理';
    }
  }

  /**
   * 批量检查并创建异常记录
   * @param options 检查选项
   */
  async batchCheckAndCreateAnomalies(
    options: {
      threshold?: number;
      absoluteThreshold?: number;
      limit?: number;
      autoCreateRecords?: boolean;
    } = {}
  ) {
    try {
      const { autoCreateRecords = true } = options;

      // 检查异常
      const checkResult = await this.checkOrderAmountAnomalies(options);

      if (!autoCreateRecords || checkResult.anomalies.length === 0) {
        return checkResult;
      }

      // 创建异常记录
      const createdRecords: OrderAmountAnomalyRecord[] = [];
      for (const anomaly of checkResult.anomalies) {
        try {
          const record = await this.createAnomalyRecord(anomaly);
          createdRecords.push(record);
        } catch (error) {
          this.logger.error(`创建异常记录失败，订单${anomaly.orderSn}:`, error);
        }
      }

      return {
        ...checkResult,
        createdRecords: createdRecords.length,
        records: createdRecords,
      };
    } catch (error) {
      this.logger.error('批量检查并创建异常记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取异常记录列表
   * @param options 查询选项
   */
  async getAnomalyRecords(
    options: {
      status?: AnomalyProcessStatus[];
      anomalyType?: OrderAmountAnomalyType[];
      severity?: number[];
      canAutoFix?: boolean;
      limit?: number;
      offset?: number;
    } = {}
  ) {
    try {
      const {
        status,
        anomalyType,
        severity,
        canAutoFix,
        limit = 50,
        offset = 0,
      } = options;

      const whereConditions: any = {};

      if (status && status.length > 0) {
        whereConditions.processStatus = { [Op.in]: status };
      }

      if (anomalyType && anomalyType.length > 0) {
        whereConditions.anomalyType = { [Op.in]: anomalyType };
      }

      if (severity && severity.length > 0) {
        whereConditions.severity = { [Op.in]: severity };
      }

      if (canAutoFix !== undefined) {
        whereConditions.canAutoFix = canAutoFix;
      }

      const { rows: records, count: total } =
        await OrderAmountAnomalyRecord.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: Order,
              attributes: ['id', 'sn', 'status', 'orderTime'],
            },
          ],
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        records,
        total,
        limit,
        offset,
      };
    } catch (error) {
      this.logger.error('获取异常记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 自动修复异常
   * @param recordId 异常记录ID
   */
  async autoFixAnomaly(recordId: number) {
    const transaction = await OrderAmountAnomalyRecord.sequelize!.transaction();

    try {
      // 获取异常记录
      const record = await OrderAmountAnomalyRecord.findByPk(recordId, {
        include: [{ model: Order, include: [OrderDetail] }],
        transaction,
      });

      if (!record) {
        throw new Error('异常记录不存在');
      }

      if (!record.canAutoFix) {
        throw new Error('该异常不支持自动修复');
      }

      // 更新记录状态为修复中
      await record.update(
        {
          processStatus: AnomalyProcessStatus.AUTO_FIXING,
          autoFixAttempts: record.autoFixAttempts + 1,
          lastFixAttemptAt: new Date(),
        },
        { transaction }
      );

      const startTime = Date.now();
      let fixResult: FixResult = FixResult.FAILED;
      let errorMessage: string | undefined;
      let beforeData: any;
      let afterData: any;

      try {
        // 执行修复操作
        const fixSuccess = await this.executeAutoFix(record, transaction);

        if (fixSuccess) {
          fixResult = FixResult.SUCCESS;
          await record.update(
            {
              processStatus: AnomalyProcessStatus.AUTO_FIXED,
              handledAt: new Date(),
            },
            { transaction }
          );
        } else {
          fixResult = FixResult.FAILED;
          await record.update(
            {
              processStatus: AnomalyProcessStatus.AUTO_FIX_FAILED,
            },
            { transaction }
          );
        }
      } catch (error) {
        fixResult = FixResult.FAILED;
        errorMessage = error.message;
        await record.update(
          {
            processStatus: AnomalyProcessStatus.AUTO_FIX_FAILED,
          },
          { transaction }
        );
      }

      // 创建修复日志
      await OrderAmountFixLog.create(
        {
          anomalyRecordId: record.id,
          orderId: record.orderId,
          orderSn: record.orderSn,
          operationType: FixOperationType.AUTO_FIX,
          result: fixResult,
          beforeData: JSON.stringify(beforeData || {}),
          afterData: JSON.stringify(afterData || {}),
          description: `自动修复${record.anomalyType}异常`,
          executionTime: Date.now() - startTime,
          errorMessage,
          operatorType: 'system',
          canRevert: fixResult === FixResult.SUCCESS,
          revertData:
            fixResult === FixResult.SUCCESS
              ? JSON.stringify(beforeData)
              : undefined,
        },
        { transaction }
      );

      await transaction.commit();

      this.logger.info(`自动修复异常记录${recordId}完成，结果: ${fixResult}`);

      return {
        success: fixResult === FixResult.SUCCESS,
        result: fixResult,
        message:
          fixResult === FixResult.SUCCESS
            ? '自动修复成功'
            : `自动修复失败: ${errorMessage}`,
      };
    } catch (error) {
      await transaction.rollback();
      this.logger.error(`自动修复异常记录${recordId}失败:`, error);
      throw error;
    }
  }

  /**
   * 执行自动修复
   * @param record 异常记录
   * @param transaction 事务
   */
  private async executeAutoFix(
    record: OrderAmountAnomalyRecord,
    transaction: Transaction
  ): Promise<boolean> {
    const order = record.order;
    if (!order) {
      throw new Error('订单信息不存在');
    }

    switch (record.anomalyType) {
      case OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE:
        if (
          record.calculatedOriginalPrice &&
          record.calculatedOriginalPrice > 0
        ) {
          await order.update(
            {
              originalPrice: record.calculatedOriginalPrice,
            },
            { transaction }
          );
          return true;
        }
        break;

      case OrderAmountAnomalyType.PRICE_MISMATCH:
        if (
          record.calculatedOriginalPrice &&
          record.calculatedOriginalPrice > 0
        ) {
          await order.update(
            {
              originalPrice: record.calculatedOriginalPrice,
            },
            { transaction }
          );
          return true;
        }
        break;

      case OrderAmountAnomalyType.CALCULATION_ERROR:
        {
          // 重新计算实付金额
          const newTotalFee =
            (order.originalPrice || 0) -
            (order.cardDeduction || 0) -
            (order.couponDeduction || 0);
          if (newTotalFee >= 0) {
            await order.update(
              {
                totalFee: newTotalFee,
              },
              { transaction }
            );
            return true;
          }
        }
        break;
    }

    return false;
  }

  /**
   * 批量自动修复异常
   * @param options 修复选项
   */
  async batchAutoFixAnomalies(
    options: {
      recordIds?: number[];
      maxAttempts?: number;
      onlyAutoFixable?: boolean;
    } = {}
  ) {
    try {
      const { recordIds, maxAttempts = 3, onlyAutoFixable = true } = options;

      // 构建查询条件
      const whereConditions: any = {
        processStatus: AnomalyProcessStatus.PENDING,
        autoFixAttempts: { [Op.lt]: maxAttempts },
      };

      if (onlyAutoFixable) {
        whereConditions.canAutoFix = true;
      }

      if (recordIds && recordIds.length > 0) {
        whereConditions.id = { [Op.in]: recordIds };
      }

      // 获取待修复的记录
      const records = await OrderAmountAnomalyRecord.findAll({
        where: whereConditions,
        limit: 50, // 限制批量处理数量
      });

      const results = {
        total: records.length,
        success: 0,
        failed: 0,
        details: [] as any[],
      };

      // 逐个修复
      for (const record of records) {
        try {
          const fixResult = await this.autoFixAnomaly(record.id);
          if (fixResult.success) {
            results.success++;
          } else {
            results.failed++;
          }
          results.details.push({
            recordId: record.id,
            orderSn: record.orderSn,
            success: fixResult.success,
            message: fixResult.message,
          });
        } catch (error) {
          results.failed++;
          results.details.push({
            recordId: record.id,
            orderSn: record.orderSn,
            success: false,
            message: error.message,
          });
        }
      }

      this.logger.info(
        `批量自动修复完成，成功${results.success}个，失败${results.failed}个`
      );
      return results;
    } catch (error) {
      this.logger.error('批量自动修复异常失败:', error);
      throw error;
    }
  }

  /**
   * 手动修复异常
   * @param recordId 异常记录ID
   * @param fixData 修复数据
   * @param operatorInfo 操作人信息
   */
  async manualFixAnomaly(
    recordId: number,
    fixData: {
      originalPrice?: number;
      totalFee?: number;
      cardDeduction?: number;
      couponDeduction?: number;
    },
    operatorInfo: {
      operatorId: number;
      operatorName: string;
      remark?: string;
    }
  ) {
    const transaction = await OrderAmountAnomalyRecord.sequelize!.transaction();

    try {
      // 获取异常记录
      const record = await OrderAmountAnomalyRecord.findByPk(recordId, {
        include: [{ model: Order }],
        transaction,
      });

      if (!record) {
        throw new Error('异常记录不存在');
      }

      if (record.processStatus === AnomalyProcessStatus.MANUAL_FIXED) {
        throw new Error('该异常已经被手动修复');
      }

      const order = record.order;
      if (!order) {
        throw new Error('订单信息不存在');
      }

      // 记录修复前的数据
      const beforeData = {
        originalPrice: order.originalPrice,
        totalFee: order.totalFee,
        cardDeduction: order.cardDeduction,
        couponDeduction: order.couponDeduction,
      };

      // 更新记录状态
      await record.update(
        {
          processStatus: AnomalyProcessStatus.MANUAL_PROCESSING,
          handlerId: operatorInfo.operatorId,
          handlerName: operatorInfo.operatorName,
          handlerRemark: operatorInfo.remark,
        },
        { transaction }
      );

      const startTime = Date.now();
      let fixResult: FixResult = FixResult.SUCCESS;
      let errorMessage: string | undefined;

      try {
        // 执行手动修复
        const updateData: any = {};
        if (fixData.originalPrice !== undefined)
          updateData.originalPrice = fixData.originalPrice;
        if (fixData.totalFee !== undefined)
          updateData.totalFee = fixData.totalFee;
        if (fixData.cardDeduction !== undefined)
          updateData.cardDeduction = fixData.cardDeduction;
        if (fixData.couponDeduction !== undefined)
          updateData.couponDeduction = fixData.couponDeduction;

        if (Object.keys(updateData).length > 0) {
          await order.update(updateData, { transaction });
        }

        // 更新记录状态为已修复
        await record.update(
          {
            processStatus: AnomalyProcessStatus.MANUAL_FIXED,
            handledAt: new Date(),
          },
          { transaction }
        );
      } catch (error) {
        fixResult = FixResult.FAILED;
        errorMessage = error.message;
        await record.update(
          {
            processStatus: AnomalyProcessStatus.PENDING,
          },
          { transaction }
        );
      }

      // 记录修复后的数据
      const afterData = {
        originalPrice: fixData.originalPrice ?? order.originalPrice,
        totalFee: fixData.totalFee ?? order.totalFee,
        cardDeduction: fixData.cardDeduction ?? order.cardDeduction,
        couponDeduction: fixData.couponDeduction ?? order.couponDeduction,
      };

      // 创建修复日志
      await OrderAmountFixLog.create(
        {
          anomalyRecordId: record.id,
          orderId: record.orderId,
          orderSn: record.orderSn,
          operationType: FixOperationType.MANUAL_FIX,
          result: fixResult,
          beforeData: JSON.stringify(beforeData),
          afterData: JSON.stringify(afterData),
          description: `手动修复${record.anomalyType}异常`,
          executionTime: Date.now() - startTime,
          errorMessage,
          operatorId: operatorInfo.operatorId,
          operatorName: operatorInfo.operatorName,
          operatorType: 'admin',
          canRevert: fixResult === FixResult.SUCCESS,
          revertData:
            fixResult === FixResult.SUCCESS
              ? JSON.stringify(beforeData)
              : undefined,
          remark: operatorInfo.remark,
        },
        { transaction }
      );

      await transaction.commit();

      this.logger.info(`手动修复异常记录${recordId}完成，结果: ${fixResult}`);

      return {
        success: fixResult === FixResult.SUCCESS,
        result: fixResult,
        message:
          fixResult === FixResult.SUCCESS
            ? '手动修复成功'
            : `手动修复失败: ${errorMessage}`,
      };
    } catch (error) {
      await transaction.rollback();
      this.logger.error(`手动修复异常记录${recordId}失败:`, error);
      throw error;
    }
  }

  /**
   * 回退修复操作
   * @param logId 修复日志ID
   * @param operatorInfo 操作人信息
   */
  async revertFix(
    logId: number,
    operatorInfo: {
      operatorId: number;
      operatorName: string;
      reason: string;
    }
  ) {
    const transaction = await OrderAmountFixLog.sequelize!.transaction();

    try {
      // 获取修复日志
      const log = await OrderAmountFixLog.findByPk(logId, {
        include: [{ model: OrderAmountAnomalyRecord }, { model: Order }],
        transaction,
      });

      if (!log) {
        throw new Error('修复日志不存在');
      }

      if (!log.canRevert) {
        throw new Error('该修复操作不支持回退');
      }

      if (log.isReverted) {
        throw new Error('该修复操作已经被回退');
      }

      if (!log.revertData) {
        throw new Error('缺少回退数据');
      }

      const order = log.order;
      const record = log.anomalyRecord;

      if (!order || !record) {
        throw new Error('关联数据不存在');
      }

      // 解析回退数据
      const revertData = JSON.parse(log.revertData);

      // 执行回退操作
      await order.update(revertData, { transaction });

      // 更新修复日志
      await log.update(
        {
          isReverted: true,
          revertedAt: new Date(),
          revertOperatorId: operatorInfo.operatorId,
          revertOperatorName: operatorInfo.operatorName,
          revertReason: operatorInfo.reason,
        },
        { transaction }
      );

      // 更新异常记录状态
      await record.update(
        {
          processStatus: AnomalyProcessStatus.PENDING,
          isReverted: true,
          revertedAt: new Date(),
          revertReason: operatorInfo.reason,
        },
        { transaction }
      );

      await transaction.commit();

      this.logger.info(`回退修复操作${logId}成功`);

      return {
        success: true,
        message: '回退操作成功',
      };
    } catch (error) {
      await transaction.rollback();
      this.logger.error(`回退修复操作${logId}失败:`, error);
      throw error;
    }
  }

  /**
   * 获取异常统计报告
   */
  async getAnomalyStatistics() {
    try {
      // 总异常数量
      const totalAnomalies = await OrderAmountAnomalyRecord.count();

      // 按状态统计
      const statusStats = await OrderAmountAnomalyRecord.findAll({
        attributes: ['processStatus', [literal('COUNT(*)'), 'count']],
        group: ['processStatus'],
        raw: true,
      });

      // 按异常类型统计
      const typeStats = await OrderAmountAnomalyRecord.findAll({
        attributes: ['anomalyType', [literal('COUNT(*)'), 'count']],
        group: ['anomalyType'],
        raw: true,
      });

      // 按严重程度统计
      const severityStats = await OrderAmountAnomalyRecord.findAll({
        attributes: ['severity', [literal('COUNT(*)'), 'count']],
        group: ['severity'],
        order: [['severity', 'ASC']],
        raw: true,
      });

      // 自动修复成功率
      const autoFixedCount = await OrderAmountAnomalyRecord.count({
        where: { processStatus: AnomalyProcessStatus.AUTO_FIXED },
      });

      const autoFixableCount = await OrderAmountAnomalyRecord.count({
        where: { canAutoFix: true },
      });

      // 最近7天的异常趋势
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentTrend = await OrderAmountAnomalyRecord.findAll({
        attributes: [
          [literal('DATE(createdAt)'), 'date'],
          [literal('COUNT(*)'), 'count'],
        ],
        where: {
          createdAt: { [Op.gte]: sevenDaysAgo },
        },
        group: ['createdAt'],
        order: [['createdAt', 'ASC']],
        raw: true,
      });

      return {
        totalAnomalies,
        statusStatistics: statusStats,
        typeStatistics: typeStats,
        severityStatistics: severityStats,
        autoFixStatistics: {
          autoFixableCount,
          autoFixedCount,
          autoFixSuccessRate:
            autoFixableCount > 0
              ? ((autoFixedCount / autoFixableCount) * 100).toFixed(2) + '%'
              : '0%',
        },
        recentTrend,
      };
    } catch (error) {
      this.logger.error('获取异常统计报告失败:', error);
      throw error;
    }
  }

  /**
   * 获取修复日志
   * @param options 查询选项
   */
  async getFixLogs(
    options: {
      anomalyRecordId?: number;
      orderId?: number;
      operationType?: FixOperationType[];
      result?: FixResult[];
      limit?: number;
      offset?: number;
    } = {}
  ) {
    try {
      const {
        anomalyRecordId,
        orderId,
        operationType,
        result,
        limit = 50,
        offset = 0,
      } = options;

      const whereConditions: any = {};

      if (anomalyRecordId) {
        whereConditions.anomalyRecordId = anomalyRecordId;
      }

      if (orderId) {
        whereConditions.orderId = orderId;
      }

      if (operationType && operationType.length > 0) {
        whereConditions.operationType = { [Op.in]: operationType };
      }

      if (result && result.length > 0) {
        whereConditions.result = { [Op.in]: result };
      }

      const { rows: logs, count: total } =
        await OrderAmountFixLog.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: OrderAmountAnomalyRecord,
              attributes: ['id', 'anomalyType', 'description'],
            },
            {
              model: Order,
              attributes: ['id', 'sn', 'status'],
            },
          ],
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        logs,
        total,
        limit,
        offset,
      };
    } catch (error) {
      this.logger.error('获取修复日志失败:', error);
      throw error;
    }
  }

  /**
   * 生成异常问题清单
   * @param options 生成选项
   */
  async generateAnomalyReport(
    options: {
      status?: AnomalyProcessStatus[];
      severity?: number[];
      includeDetails?: boolean;
    } = {}
  ) {
    try {
      const {
        status = [
          AnomalyProcessStatus.PENDING,
          AnomalyProcessStatus.MANUAL_REQUIRED,
        ],
        severity,
        includeDetails = true,
      } = options;

      const whereConditions: any = {
        processStatus: { [Op.in]: status },
      };

      if (severity && severity.length > 0) {
        whereConditions.severity = { [Op.in]: severity };
      }

      const records = await OrderAmountAnomalyRecord.findAll({
        where: whereConditions,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'status', 'orderTime', 'customerId'],
          },
        ],
        order: [
          ['severity', 'DESC'],
          ['createdAt', 'DESC'],
        ],
      });

      const report = {
        generateTime: new Date(),
        totalCount: records.length,
        summary: {
          bySeverity: {} as any,
          byType: {} as any,
          byStatus: {} as any,
        },
        issues: records.map(record => ({
          id: record.id,
          orderId: record.orderId,
          orderSn: record.orderSn,
          anomalyType: record.anomalyType,
          description: record.description,
          severity: record.severity,
          processStatus: record.processStatus,
          anomalyAmount: record.anomalyAmount,
          canAutoFix: record.canAutoFix,
          fixSuggestion: record.fixSuggestion,
          createdAt: record.createdAt,
          orderInfo: record.order
            ? {
                status: record.order.status,
                orderTime: record.order.orderTime,
                customerId: record.order.customerId,
              }
            : null,
          details: includeDetails
            ? JSON.parse(record.anomalyDetails)
            : undefined,
        })),
      };

      // 生成汇总统计
      records.forEach(record => {
        // 按严重程度统计
        const severityKey = `severity_${record.severity}`;
        report.summary.bySeverity[severityKey] =
          (report.summary.bySeverity[severityKey] || 0) + 1;

        // 按类型统计
        report.summary.byType[record.anomalyType] =
          (report.summary.byType[record.anomalyType] || 0) + 1;

        // 按状态统计
        report.summary.byStatus[record.processStatus] =
          (report.summary.byStatus[record.processStatus] || 0) + 1;
      });

      this.logger.info(`生成异常问题清单完成，共${records.length}个问题`);
      return report;
    } catch (error) {
      this.logger.error('生成异常问题清单失败:', error);
      throw error;
    }
  }

  /**
   * 忽略异常记录
   * @param recordId 异常记录ID
   * @param operatorInfo 操作人信息
   */
  async ignoreAnomaly(
    recordId: number,
    operatorInfo: {
      operatorId: number;
      operatorName: string;
      reason: string;
    }
  ) {
    try {
      const record = await OrderAmountAnomalyRecord.findByPk(recordId);

      if (!record) {
        throw new Error('异常记录不存在');
      }

      if (record.processStatus === AnomalyProcessStatus.IGNORED) {
        throw new Error('该异常已经被忽略');
      }

      await record.update({
        processStatus: AnomalyProcessStatus.IGNORED,
        handlerId: operatorInfo.operatorId,
        handlerName: operatorInfo.operatorName,
        handledAt: new Date(),
        handlerRemark: `忽略原因: ${operatorInfo.reason}`,
      });

      this.logger.info(`忽略异常记录${recordId}成功`);

      return {
        success: true,
        message: '异常记录已忽略',
      };
    } catch (error) {
      this.logger.error(`忽略异常记录${recordId}失败:`, error);
      throw error;
    }
  }
}
