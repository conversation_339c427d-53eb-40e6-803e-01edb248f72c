# 订单金额异常检查API文档

## 概述

订单金额异常检查功能用于检测、修复和管理订单金额异常，包括原价与实付不匹配、优惠金额异常等问题。系统支持自动修复和手动修复，并提供完整的处理记录和回退功能。

## API接口

### 1. 检查订单金额异常

**接口地址：** `GET /admin/order-amount-anomalies/check`

**接口描述：** 检查订单金额异常，返回异常列表（自动排除已取消、退款中、已退款的订单）

**请求参数：**
- `threshold` (number, 可选): 异常阈值百分比，默认1%，设为0则检测任何不一致
- `absoluteThreshold` (number, 可选): 绝对金额阈值（元），优先级高于百分比阈值
- `limit` (number, 可选): 返回记录数限制，默认100
- `orderId` (number, 可选): 指定订单ID
- `skipExisting` (string, 可选): 是否跳过已记录的异常，默认true
- `clearExistingRecords` (string, 可选): 是否在检测前清除所有异常记录，默认false

**响应示例：**
```json
{
  "anomalyCount": 5,
  "threshold": 1,
  "anomalies": [
    {
      "orderId": 123,
      "orderSn": "ORD20231201001",
      "anomalyType": "price_mismatch",
      "description": "订单原价与计算价格不匹配，差异5.2%",
      "severity": 2,
      "canAutoFix": true,
      "calculatedOriginalPrice": 100.00,
      "anomalyAmount": 5.20,
      "fixSuggestion": "【原价不匹配】当前原价: 105.20元，高于计算值 5.20元。计算依据：服务项目总价 100.00元。建议修复：将原价调整为 100.00元",
      "currentData": {
        "originalPrice": 105.20,
        "totalFee": 95.00,
        "cardDeduction": 10.00,
        "couponDeduction": 0.00
      }
    }
  ]
}
```

### 2. 批量检查并创建异常记录

**接口地址：** `POST /admin/order-amount-anomalies/batch-check`

**接口描述：** 批量检查异常并自动创建异常记录

**请求参数：**
- `threshold` (number, 可选): 异常阈值百分比，设为0则检测任何不一致
- `absoluteThreshold` (number, 可选): 绝对金额阈值（元），优先级高于百分比阈值
- `limit` (number, 可选): 检查记录数限制
- `autoCreateRecords` (string, 可选): 是否自动创建记录，默认true
- `clearExistingRecords` (string, 可选): 是否在检测前清除所有异常记录，默认false

### 3. 获取异常记录列表

**接口地址：** `GET /admin/order-amount-anomalies/records`

**接口描述：** 获取异常记录列表

**请求参数：**
- `status` (string, 可选): 处理状态，多个用逗号分隔
- `anomalyType` (string, 可选): 异常类型，多个用逗号分隔
- `severity` (string, 可选): 严重程度，多个用逗号分隔
- `canAutoFix` (boolean, 可选): 是否可自动修复
- `limit` (number, 可选): 返回记录数限制，默认50
- `offset` (number, 可选): 偏移量，默认0

### 4. 自动修复异常

**接口地址：** `POST /admin/order-amount-anomalies/auto-fix/{recordId}`

**接口描述：** 自动修复指定的异常记录

**路径参数：**
- `recordId` (number): 异常记录ID

**响应示例：**
```json
{
  "success": true,
  "result": "success",
  "message": "自动修复成功"
}
```

### 5. 批量自动修复异常

**接口地址：** `POST /admin/order-amount-anomalies/batch-auto-fix`

**接口描述：** 批量自动修复异常

**请求参数：**
- `recordIds` (string, 可选): 异常记录ID列表，用逗号分隔
- `maxAttempts` (number, 可选): 最大尝试次数，默认3
- `onlyAutoFixable` (string, 可选): 只修复可自动修复的，默认true

### 6. 手动修复异常

**接口地址：** `POST /admin/order-amount-anomalies/manual-fix/{recordId}`

**接口描述：** 手动修复指定的异常记录

**路径参数：**
- `recordId` (number): 异常记录ID

**请求体：**
```json
{
  "originalPrice": 100.00,
  "totalFee": 90.00,
  "cardDeduction": 10.00,
  "couponDeduction": 0.00,
  "operatorId": 1,
  "operatorName": "管理员",
  "remark": "手动修复原价错误"
}
```

### 7. 回退修复操作

**接口地址：** `POST /admin/order-amount-anomalies/revert/{logId}`

**接口描述：** 回退指定的修复操作

**路径参数：**
- `logId` (number): 修复日志ID

**请求体：**
```json
{
  "operatorId": 1,
  "operatorName": "管理员",
  "reason": "修复错误，需要回退"
}
```

### 8. 获取异常统计报告

**接口地址：** `GET /admin/order-amount-anomalies/statistics`

**接口描述：** 获取异常统计报告

**响应示例：**
```json
{
  "totalAnomalies": 50,
  "statusStatistics": [
    {"processStatus": "pending", "count": 20},
    {"processStatus": "auto_fixed", "count": 25}
  ],
  "typeStatistics": [
    {"anomalyType": "price_mismatch", "count": 30},
    {"anomalyType": "missing_original_price", "count": 15}
  ],
  "autoFixStatistics": {
    "autoFixableCount": 35,
    "autoFixedCount": 25,
    "autoFixSuccessRate": "71.43%"
  }
}
```

### 9. 获取修复日志

**接口地址：** `GET /admin/order-amount-anomalies/fix-logs`

**接口描述：** 获取修复操作日志

**请求参数：**
- `anomalyRecordId` (number, 可选): 异常记录ID
- `orderId` (number, 可选): 订单ID
- `operationType` (string, 可选): 操作类型
- `result` (string, 可选): 修复结果
- `limit` (number, 可选): 返回记录数限制
- `offset` (number, 可选): 偏移量

### 10. 生成异常问题清单

**接口地址：** `GET /admin/order-amount-anomalies/report`

**接口描述：** 生成异常问题清单报告

**请求参数：**
- `status` (string, 可选): 处理状态过滤
- `severity` (string, 可选): 严重程度过滤
- `includeDetails` (string, 可选): 是否包含详细信息，默认true

### 11. 忽略异常记录

**接口地址：** `POST /admin/order-amount-anomalies/ignore/{recordId}`

**接口描述：** 忽略指定的异常记录

**路径参数：**
- `recordId` (number): 异常记录ID

**请求体：**
```json
{
  "operatorId": 1,
  "operatorName": "管理员",
  "reason": "业务正常，忽略此异常"
}
```

## 异常类型说明

- `price_mismatch`: 原价与实付不匹配
- `discount_anomaly`: 优惠金额异常
- `missing_original_price`: 原价缺失
- `calculation_error`: 计算错误
- `other`: 其他异常

## 处理状态说明

- `pending`: 待处理
- `auto_fixing`: 自动修复中
- `auto_fixed`: 自动修复成功
- `auto_fix_failed`: 自动修复失败
- `manual_required`: 需要人工处理
- `manual_processing`: 人工处理中
- `manual_fixed`: 人工处理完成
- `ignored`: 已忽略

## 修复建议说明

系统会为每种异常类型生成详细的修复建议：

### 1. 原价缺失 (MISSING_ORIGINAL_PRICE)
```
【原价缺失】当前原价: 0元，根据服务项目计算应为: 100.00元。建议修复：将原价设置为 100.00元
```

### 2. 原价不匹配 (PRICE_MISMATCH)
```
【原价不匹配】当前原价: 105.20元，高于计算值 5.20元。计算依据：服务项目总价 100.00元。建议修复：将原价调整为 100.00元
```

### 3. 实付金额异常 (CALCULATION_ERROR)
```
【实付金额异常】当前实付: 95.00元，高于预期值 5.00元。计算公式：原价(100.00) - 权益卡抵扣(10.00) - 代金券抵扣(0.00) = 90.00元。建议修复：将实付金额调整为 90.00元
```

### 4. 优惠金额异常 (DISCOUNT_ANOMALY)
```
【优惠金额异常】总优惠金额 120.00元 超过原价 100.00元。详情：权益卡抵扣 80.00元 + 代金券抵扣 40.00元 = 120.00元。建议检查：1.权益卡使用规则 2.代金券使用条件 3.是否存在重复抵扣
```

## 异常检测策略

### 阈值设置说明

1. **百分比阈值** (`threshold`):
   - 默认值: 1（表示1%）
   - 设为0: 检测任何金额不一致，哪怕差1分钱
   - 设为5: 只检测差异超过5%的异常

2. **绝对金额阈值** (`absoluteThreshold`):
   - 单位: 元
   - 优先级高于百分比阈值
   - 例如设为1: 只检测差异超过1元的异常

### 使用示例

```bash
# 检测任何不一致（最严格）
GET /admin/order-amount-anomalies/check?threshold=0

# 检测差异超过1元的异常
GET /admin/order-amount-anomalies/check?absoluteThreshold=1

# 检测差异超过5%的异常
GET /admin/order-amount-anomalies/check?threshold=5
```

## 使用流程

1. **定期检查**: 使用批量检查接口定期扫描订单异常
2. **自动修复**: 对可自动修复的异常执行自动修复
3. **人工处理**: 对无法自动修复的异常进行人工处理
4. **监控统计**: 通过统计报告监控异常情况
5. **问题清单**: 生成待处理问题清单供运营人员处理
6. **回退机制**: 必要时可回退错误的修复操作

## 其他接口

### 清理无效异常记录

**接口地址：** `POST /admin/order-amount-anomalies/cleanup-invalid`

**接口描述：** 清理已取消、待付款订单的异常记录（历史数据清理）

### 更新修复建议

**接口地址：** `POST /admin/order-amount-anomalies/update-fix-suggestions`

**接口描述：** 更新现有异常记录的修复建议为新的详细格式

**响应示例：**
```json
{
  "message": "成功更新了 25 条记录的修复建议",
  "updatedCount": 25,
  "totalRecords": 25
}
```
