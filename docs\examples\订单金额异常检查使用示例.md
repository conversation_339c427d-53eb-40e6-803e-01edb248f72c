# 订单金额异常检查功能使用示例

## 1. 基本使用流程

### 1.1 检查订单异常

```bash
# 检查所有订单的金额异常（使用默认1%阈值）
curl -X GET "http://localhost:7001/admin/order-amount-anomalies/check"

# 检查特定订单
curl -X GET "http://localhost:7001/admin/order-amount-anomalies/check?orderId=123"

# 使用自定义阈值检查
curl -X GET "http://localhost:7001/admin/order-amount-anomalies/check?threshold=5&limit=50"
```

### 1.2 批量检查并创建异常记录

```bash
# 批量检查并自动创建异常记录
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/batch-check?threshold=2&limit=100"
```

### 1.3 查看异常记录

```bash
# 获取所有待处理的异常记录
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/records?status=pending"

# 获取可自动修复的异常记录
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/records?canAutoFix=true"

# 获取高严重级别的异常
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/records?severity=4,5"
```

## 2. 修复操作示例

### 2.1 自动修复

```bash
# 修复单个异常记录
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/auto-fix/1"

# 批量自动修复所有可修复的异常
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/batch-auto-fix?onlyAutoFixable=true"

# 批量修复指定的异常记录
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/batch-auto-fix?recordIds=1,2,3"
```

### 2.2 手动修复

```bash
# 手动修复异常记录
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/manual-fix/1" \
  -H "Content-Type: application/json" \
  -d '{
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 10.00,
    "couponDeduction": 0.00,
    "operatorId": 1,
    "operatorName": "管理员张三",
    "remark": "根据订单详情重新计算原价"
  }'
```

### 2.3 回退修复操作

```bash
# 回退错误的修复操作
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/revert/1" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "operatorName": "管理员张三",
    "reason": "修复方案有误，需要重新分析"
  }'
```

## 3. 监控和报告

### 3.1 获取统计信息

```bash
# 获取异常统计报告
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/statistics"
```

响应示例：
```json
{
  "totalAnomalies": 50,
  "statusStatistics": [
    {"processStatus": "pending", "count": 20},
    {"processStatus": "auto_fixed", "count": 25},
    {"processStatus": "manual_fixed", "count": 3},
    {"processStatus": "ignored", "count": 2}
  ],
  "typeStatistics": [
    {"anomalyType": "price_mismatch", "count": 30},
    {"anomalyType": "missing_original_price", "count": 15},
    {"anomalyType": "calculation_error", "count": 5}
  ],
  "severityStatistics": [
    {"severity": 1, "count": 10},
    {"severity": 2, "count": 20},
    {"severity": 3, "count": 15},
    {"severity": 4, "count": 4},
    {"severity": 5, "count": 1}
  ],
  "autoFixStatistics": {
    "autoFixableCount": 35,
    "autoFixedCount": 25,
    "autoFixSuccessRate": "71.43%"
  }
}
```

### 3.2 生成问题清单

```bash
# 生成待处理问题清单
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/report?status=pending,manual_required"

# 生成高严重级别问题清单
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/report?severity=4,5&includeDetails=true"
```

### 3.3 查看修复日志

```bash
# 查看所有修复日志
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/fix-logs"

# 查看特定订单的修复日志
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/fix-logs?orderId=123"

# 查看自动修复的日志
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/fix-logs?operationType=auto_fix"
```

## 4. 典型业务场景

### 4.1 日常巡检

```bash
#!/bin/bash
# 每日异常检查脚本

echo "开始每日订单金额异常检查..."

# 1. 检查新的异常
echo "检查新的异常..."
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/batch-check?threshold=1&limit=1000"

# 2. 自动修复可修复的异常
echo "执行自动修复..."
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/batch-auto-fix?onlyAutoFixable=true"

# 3. 获取统计报告
echo "生成统计报告..."
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/statistics" > daily_anomaly_report.json

# 4. 生成待处理问题清单
echo "生成待处理问题清单..."
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/report?status=pending,manual_required" > pending_issues.json

echo "每日检查完成！"
```

### 4.2 应急处理

```bash
#!/bin/bash
# 应急异常处理脚本

echo "开始应急异常处理..."

# 1. 检查特定时间段的订单
echo "检查最近24小时的订单..."
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/batch-check?threshold=0.5&limit=5000"

# 2. 获取高严重级别异常
echo "获取高严重级别异常..."
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/records?severity=4,5&limit=100" > critical_anomalies.json

# 3. 暂停自动修复，等待人工处理
echo "请人工检查critical_anomalies.json文件中的异常！"
```

### 4.3 数据修复

```bash
#!/bin/bash
# 批量数据修复脚本

echo "开始批量数据修复..."

# 1. 修复原价缺失的订单
echo "修复原价缺失的订单..."
curl -X GET "http://localhost:7001/admin/order-amount-anomaly/records?anomalyType=missing_original_price&canAutoFix=true" | \
jq -r '.records[].id' | \
while read recordId; do
  echo "修复记录 $recordId"
  curl -X POST "http://localhost:7001/admin/order-amount-anomaly/auto-fix/$recordId"
done

echo "批量修复完成！"
```

## 5. 错误处理

### 5.1 常见错误

```bash
# 错误：异常记录不存在
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/auto-fix/999"
# 响应：{"errCode": "BUSINESS_ERROR", "msg": "异常记录不存在"}

# 错误：该异常不支持自动修复
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/auto-fix/1"
# 响应：{"errCode": "BUSINESS_ERROR", "msg": "该异常不支持自动修复"}

# 错误：修复操作已被回退
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/revert/1"
# 响应：{"errCode": "BUSINESS_ERROR", "msg": "该修复操作已经被回退"}
```

### 5.2 错误恢复

```bash
# 如果自动修复失败，可以尝试手动修复
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/manual-fix/1" \
  -H "Content-Type: application/json" \
  -d '{
    "originalPrice": 100.00,
    "operatorId": 1,
    "operatorName": "管理员",
    "remark": "自动修复失败，手动处理"
  }'

# 如果修复错误，可以回退操作
curl -X POST "http://localhost:7001/admin/order-amount-anomaly/revert/1" \
  -H "Content-Type: application/json" \
  -d '{
    "operatorId": 1,
    "operatorName": "管理员",
    "reason": "修复方案错误"
  }'
```

## 6. 最佳实践

1. **定期检查**: 建议每日执行异常检查
2. **优先级处理**: 优先处理高严重级别的异常
3. **谨慎自动修复**: 对于重要订单，建议人工确认后再修复
4. **保留记录**: 所有修复操作都会自动记录，便于审计
5. **监控趋势**: 定期查看统计报告，分析异常趋势
6. **及时处理**: 避免异常积累，影响数据质量
