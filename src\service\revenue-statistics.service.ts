import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col, literal, QueryTypes } from 'sequelize';
import { Order, AdditionalServiceOrder, Employee } from '../entity';
import { OrderStatus } from '../common/Constant';
import { AdditionalServiceOrderStatus } from '../entity/additional-service-order.entity';

@Provide()
export class RevenueStatisticsService {
  @Inject()
  ctx: Context;

  /**
   * 获取收入概览统计
   */
  async getRevenueOverview(startDate?: string, endDate?: string) {
    // 构建时间条件
    let timeCondition = '';
    const params: any[] = [];

    if (startDate && endDate) {
      timeCondition = 'AND o.createdAt BETWEEN ? AND ?';
      params.push(new Date(startDate), new Date(endDate + ' 23:59:59'));
    }

    // 数据一致性验证查询
    const validationQuery = `
      SELECT
        COUNT(*) as totalCount,
        COUNT(CASE WHEN ABS(o.totalFee - (o.originalPrice - o.cardDeduction - o.couponDeduction)) > 0.01 THEN 1 END) as inconsistentCount,
        SUM(o.originalPrice) as sumOriginalPrice,
        SUM(o.cardDeduction + o.couponDeduction) as sumDiscounts,
        SUM(o.totalFee) as sumTotalFee,
        SUM(o.originalPrice - o.cardDeduction - o.couponDeduction) as calculatedTotalFee
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
      ${timeCondition}
    `;

    const validationResult = (await Order.sequelize.query(validationQuery, {
      replacements: params,
      type: QueryTypes.SELECT,
    })) as any[];

    console.log('=== 主订单数据一致性验证 ===');
    console.log('总订单数:', validationResult[0].totalCount);
    console.log('数据不一致订单数:', validationResult[0].inconsistentCount);
    console.log('原价总和:', validationResult[0].sumOriginalPrice);
    console.log('优惠总和:', validationResult[0].sumDiscounts);
    console.log('实付总和:', validationResult[0].sumTotalFee);
    console.log('计算实付总和:', validationResult[0].calculatedTotalFee);
    console.log('差异:', parseFloat(validationResult[0].sumTotalFee) - parseFloat(validationResult[0].calculatedTotalFee));

    // 主订单收入统计 - 包含已完成、已评价、退款中、已退款
    const mainOrderQuery = `
      SELECT
        COUNT(o.id) as orderCount,
        SUM(o.originalPrice) as totalOriginalPrice,
        SUM(o.totalFee) as totalPaidAmount,
        SUM(o.cardDeduction) as totalCardDeduction,
        SUM(o.couponDeduction) as totalCouponDeduction,
        SUM(CASE WHEN o.status IN ('已完成', '已评价') THEN o.totalFee ELSE 0 END) as effectiveRevenue,
        SUM(CASE WHEN o.status IN ('退款中', '已退款') THEN o.totalFee ELSE 0 END) as refundedAmount,
        COUNT(CASE WHEN o.status IN ('已完成', '已评价') THEN 1 ELSE NULL END) as effectiveOrderCount,
        COUNT(CASE WHEN o.status IN ('退款中', '已退款') THEN 1 ELSE NULL END) as refundedOrderCount
      FROM orders o
      WHERE o.status IN ('已完成', '已评价', '退款中', '已退款')
      ${timeCondition}
    `;

    const mainOrderStats = (await Order.sequelize.query(mainOrderQuery, {
      replacements: params,
      type: QueryTypes.SELECT,
    })) as any[];

    // 追加服务收入统计
    const additionalServiceQuery = `
      SELECT
        COUNT(aso.id) as orderCount,
        SUM(aso.originalPrice) as totalOriginalPrice,
        SUM(aso.totalFee) as totalPaidAmount,
        SUM(aso.cardDeduction) as totalCardDeduction,
        SUM(aso.couponDeduction) as totalCouponDeduction,
        SUM(CASE WHEN aso.status = '已完成' THEN aso.totalFee ELSE 0 END) as effectiveRevenue,
        SUM(CASE WHEN aso.status IN ('退款中', '已退款') THEN aso.totalFee ELSE 0 END) as refundedAmount,
        COUNT(CASE WHEN aso.status = '已完成' THEN 1 ELSE NULL END) as effectiveOrderCount,
        COUNT(CASE WHEN aso.status IN ('退款中', '已退款') THEN 1 ELSE NULL END) as refundedOrderCount
      FROM additional_service_orders aso
      WHERE aso.status IN ('已完成', '退款中', '已退款')
      ${timeCondition.replace('o.', 'aso.')}
    `;

    const additionalServiceStats = (await Order.sequelize.query(
      additionalServiceQuery,
      {
        replacements: params,
        type: QueryTypes.SELECT,
      }
    )) as any[];

    // 获取统计结果
    const mainStats = mainOrderStats[0] || {};
    const additionalStats = additionalServiceStats[0] || {};

    // 按照6个核心概念计算数据

    // 1. 总原价：所有订单的原价（包含退款订单）
    const mainOriginalPrice = parseFloat(mainStats.totalOriginalPrice || '0');
    const additionalOriginalPrice = parseFloat(
      additionalStats.totalOriginalPrice || '0'
    );
    const totalOriginalPrice = mainOriginalPrice + additionalOriginalPrice;

    // 2. 优惠金额：所有订单的优惠（包含退款订单）
    const mainCardDeduction = parseFloat(mainStats.totalCardDeduction || '0');
    const additionalCardDeduction = parseFloat(
      additionalStats.totalCardDeduction || '0'
    );
    const mainCouponDeduction = parseFloat(
      mainStats.totalCouponDeduction || '0'
    );
    const additionalCouponDeduction = parseFloat(
      additionalStats.totalCouponDeduction || '0'
    );
    const totalDiscount =
      mainCardDeduction +
      additionalCardDeduction +
      mainCouponDeduction +
      additionalCouponDeduction;

    // 3. 优惠率：优惠金额/总原价
    const discountRate = totalOriginalPrice > 0
      ? ((totalDiscount / totalOriginalPrice) * 100).toFixed(2)
      : '0.00';

    // 4. 实收金额：所有订单的实付总和（应该等于总原价-优惠金额）
    const mainPaidAmount = parseFloat(mainStats.totalPaidAmount || '0');
    const additionalPaidAmount = parseFloat(
      additionalStats.totalPaidAmount || '0'
    );
    const totalPaidAmount = mainPaidAmount + additionalPaidAmount;

    // 5. 退款金额：退款订单的实付金额
    const mainRefundedAmount = parseFloat(mainStats.refundedAmount || '0');
    const additionalRefundedAmount = parseFloat(
      additionalStats.refundedAmount || '0'
    );
    const totalRefundedAmount = mainRefundedAmount + additionalRefundedAmount;

    // 6. 净收入：实收金额 - 退款金额
    const netRevenue = totalPaidAmount - totalRefundedAmount;

    // 计算订单数量
    const mainOrderCount = parseInt(mainStats.orderCount || '0');
    const additionalOrderCount = parseInt(additionalStats.orderCount || '0');

    return {
      // 按照6个核心概念返回数据
      totalOriginalPrice,           // 1. 总原价
      totalDiscount,               // 2. 优惠金额
      discountRate,                // 3. 优惠率
      totalPaidAmount,             // 4. 实收金额
      totalRefundedAmount,         // 5. 退款金额
      netRevenue,                  // 6. 净收入

      // 主订单数据
      mainOrder: {
        orderCount: mainOrderCount,
        paidAmount: mainPaidAmount,
        refundedAmount: mainRefundedAmount,
        totalOriginalPrice: mainOriginalPrice,
        totalDiscount: mainCardDeduction + mainCouponDeduction,
        avgOrderValue:
          mainOrderCount > 0
            ? (mainPaidAmount / mainOrderCount).toFixed(2)
            : '0.00',
      },

      // 追加服务数据
      additionalService: {
        orderCount: additionalOrderCount,
        paidAmount: additionalPaidAmount,
        refundedAmount: additionalRefundedAmount,
        totalOriginalPrice: additionalOriginalPrice,
        totalDiscount: additionalCardDeduction + additionalCouponDeduction,
        avgOrderValue:
          additionalOrderCount > 0
            ? (additionalPaidAmount / additionalOrderCount).toFixed(2)
            : '0.00',
      },

      // 总订单数据
      totalOrderCount: mainOrderCount + additionalOrderCount,
      avgOrderValue:
        mainOrderCount + additionalOrderCount > 0
          ? (totalPaidAmount / (mainOrderCount + additionalOrderCount)).toFixed(2)
          : '0.00',
    };
  }

  /**
   * 获取收入趋势统计
   */
  async getRevenueTrend(
    startDate: string,
    endDate: string,
    periodType: 'day' | 'week' | 'month' = 'day'
  ) {
    let dateFormat: string;

    switch (periodType) {
      case 'week':
        dateFormat = '%Y-%u';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    const whereCondition = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
      createdAt: {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      },
    };

    // 主订单趋势
    const mainOrderTrend = await Order.findAll({
      where: whereCondition,
      attributes: [
        [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
      ],
      group: ['period'],
      order: [['period', 'ASC']],
      raw: true,
    });

    // 追加服务趋势
    const additionalServiceTrend = await AdditionalServiceOrder.findAll({
      where: {
        status: {
          [Op.in]: [AdditionalServiceOrderStatus.COMPLETED],
        },
        createdAt: {
          [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
        },
      },
      attributes: [
        [literal(`DATE_FORMAT(createdAt, '${dateFormat}')`), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
      ],
      group: ['period'],
      order: [['period', 'ASC']],
      raw: true,
    });

    // 合并数据
    const trendMap = new Map();

    // 处理主订单数据
    mainOrderTrend.forEach((item: any) => {
      const period = item.period;
      trendMap.set(period, {
        period,
        mainOrder: {
          orderCount: parseInt(item.orderCount),
          totalRevenue: parseFloat(item.totalRevenue || '0'),
          totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
          totalDiscount:
            parseFloat(item.totalCardDeduction || '0') +
            parseFloat(item.totalCouponDeduction || '0'),
        },
        additionalService: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
      });
    });

    // 处理追加服务数据
    additionalServiceTrend.forEach((item: any) => {
      const period = item.period;
      const existing = trendMap.get(period) || {
        period,
        mainOrder: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
        additionalService: {
          orderCount: 0,
          totalRevenue: 0,
          totalOriginalPrice: 0,
          totalDiscount: 0,
        },
      };

      existing.additionalService = {
        orderCount: parseInt(item.orderCount),
        totalRevenue: parseFloat(item.totalRevenue || '0'),
        totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
        totalDiscount:
          parseFloat(item.totalCardDeduction || '0') +
          parseFloat(item.totalCouponDeduction || '0'),
      };

      trendMap.set(period, existing);
    });

    // 转换为数组并计算总计
    const trendData = Array.from(trendMap.values()).map((item: any) => ({
      ...item,
      totalRevenue:
        item.mainOrder.totalRevenue + item.additionalService.totalRevenue,
      totalOriginalPrice:
        item.mainOrder.totalOriginalPrice +
        item.additionalService.totalOriginalPrice,
      totalDiscount:
        item.mainOrder.totalDiscount + item.additionalService.totalDiscount,
      totalOrderCount:
        item.mainOrder.orderCount + item.additionalService.orderCount,
    }));

    return trendData;
  }

  /**
   * 获取服务收入统计
   */
  async getServiceRevenueStatistics(
    startDate?: string,
    endDate?: string,
    serviceTypeId?: number,
    serviceId?: number,
    page = 1,
    pageSize = 20,
    sortBy: 'totalRevenue' | 'orderCount' | 'avgRevenue' = 'totalRevenue',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    // 构建查询条件
    let whereClause = `
      WHERE o.status IN ('已完成', '已评价')
    `;

    const params: any[] = [];

    if (startDate && endDate) {
      whereClause += ` AND o.createdAt BETWEEN ? AND ?`;
      params.push(new Date(startDate), new Date(endDate + ' 23:59:59'));
    }

    if (serviceTypeId) {
      whereClause += ` AND s.serviceTypeId = ?`;
      params.push(serviceTypeId);
    }

    if (serviceId) {
      whereClause += ` AND od.serviceId = ?`;
      params.push(serviceId);
    }

    // 构建排序字段
    let orderField = 'totalRevenue';
    switch (sortBy) {
      case 'orderCount':
        orderField = 'orderCount';
        break;
      case 'avgRevenue':
        orderField = 'avgRevenue';
        break;
      default:
        orderField = 'totalRevenue';
    }

    // 使用原生SQL查询获取服务收入统计
    const query = `
      SELECT
        od.serviceId,
        od.serviceName,
        od.servicePrice as basePrice,
        st.name as serviceType,
        st.id as serviceTypeId,
        COUNT(od.id) as totalOrderCount,
        COUNT(CASE WHEN o.status IN ('已完成', '已评价') THEN 1 ELSE NULL END) as effectiveOrderCount,
        COUNT(CASE WHEN o.status IN ('退款中', '已退款') THEN 1 ELSE NULL END) as refundedOrderCount,
        SUM(od.servicePrice) as totalOriginalPrice,
        SUM(CASE WHEN o.status IN ('已完成', '已评价') THEN od.servicePrice ELSE 0 END) as effectiveRevenue,
        SUM(CASE WHEN o.status IN ('退款中', '已退款') THEN od.servicePrice ELSE 0 END) as refundedAmount,
        AVG(CASE WHEN o.status IN ('已完成', '已评价') THEN od.servicePrice ELSE NULL END) as avgRevenue
      FROM order_details od
      INNER JOIN orders o ON od.orderId = o.id
      LEFT JOIN services s ON od.serviceId = s.id
      LEFT JOIN service_types st ON s.serviceTypeId = st.id
      ${whereClause}
      GROUP BY od.serviceId, od.serviceName, od.servicePrice, st.id, st.name
      ORDER BY ${orderField} ${sortOrder.toUpperCase()}
      LIMIT ? OFFSET ?
    `;

    params.push(pageSize, (page - 1) * pageSize);

    const serviceStats = (await Order.sequelize.query(query, {
      replacements: params,
      type: QueryTypes.SELECT,
    })) as any[];

    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT od.serviceId) as total
      FROM order_details od
      INNER JOIN orders o ON od.orderId = o.id
      LEFT JOIN services s ON od.serviceId = s.id
      LEFT JOIN service_types st ON s.serviceTypeId = st.id
      ${whereClause}
    `;

    const countParams = params.slice(0, -2); // 移除LIMIT和OFFSET参数
    const totalResult = (await Order.sequelize.query(countQuery, {
      replacements: countParams,
      type: QueryTypes.SELECT,
    })) as any[];

    const formattedStats = serviceStats.map((item: any) => ({
      serviceId: item.serviceId,
      serviceName: item.serviceName,
      serviceType: item.serviceType || '未知类型',
      serviceTypeId: item.serviceTypeId,
      totalOrderCount: parseInt(item.totalOrderCount || '0'),
      effectiveOrderCount: parseInt(item.effectiveOrderCount || '0'),
      refundedOrderCount: parseInt(item.refundedOrderCount || '0'),
      totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
      effectiveRevenue: parseFloat(item.effectiveRevenue || '0'),
      refundedAmount: parseFloat(item.refundedAmount || '0'),
      netRevenue:
        parseFloat(item.effectiveRevenue || '0') -
        parseFloat(item.refundedAmount || '0'),
      avgRevenue: parseFloat(item.avgRevenue || '0'),
      basePrice: parseFloat(item.basePrice || '0'),
    }));

    return {
      list: formattedStats,
      total: parseInt(totalResult[0]?.total || '0'),
      page,
      pageSize,
    };
  }

  /**
   * 获取员工收入统计
   */
  async getEmployeeRevenueStatistics(
    startDate?: string,
    endDate?: string,
    employeeId?: number,
    page = 1,
    pageSize = 20,
    sortBy: 'totalRevenue' | 'orderCount' | 'avgRevenue' = 'totalRevenue',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    const whereCondition: any = {
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    // 主订单员工统计
    const employeeStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'employeeId',
        [fn('COUNT', col('Order.id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
        [fn('AVG', col('totalFee')), 'avgRevenue'],
      ],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar', 'rating'],
          required: true,
        },
      ],
      group: ['employeeId', 'employee.id'],
      order: [[fn('SUM', col('totalFee')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取追加服务收入
    const additionalServiceRevenue = await AdditionalServiceOrder.findAll({
      where: {
        status: {
          [Op.in]: [AdditionalServiceOrderStatus.COMPLETED],
        },
        ...(startDate &&
          endDate && {
            createdAt: {
              [Op.between]: [
                new Date(startDate),
                new Date(endDate + ' 23:59:59'),
              ],
            },
          }),
        ...(employeeId && { employeeId }),
      },
      attributes: [
        'employeeId',
        [fn('COUNT', col('id')), 'additionalOrderCount'],
        [fn('SUM', col('totalFee')), 'additionalRevenue'],
        [fn('SUM', col('originalPrice')), 'additionalOriginalPrice'],
        [fn('SUM', col('cardDeduction')), 'additionalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'additionalCouponDeduction'],
      ],
      group: ['employeeId'],
      raw: true,
    });

    // 合并数据
    const additionalMap = new Map();
    additionalServiceRevenue.forEach((item: any) => {
      additionalMap.set(item.employeeId, item);
    });

    const formattedStats = employeeStats.map((item: any) => {
      const additional = additionalMap.get(item.employeeId) || {};
      const mainRevenue = parseFloat(item.totalRevenue || '0');
      const additionalRev = parseFloat(additional.additionalRevenue || '0');
      const totalRevenue = mainRevenue + additionalRev;

      return {
        employeeId: item.employeeId,
        employee: item.employee,
        mainOrder: {
          orderCount: parseInt(item.orderCount),
          totalRevenue: mainRevenue,
          totalOriginalPrice: parseFloat(item.totalOriginalPrice || '0'),
          totalDiscount:
            parseFloat(item.totalCardDeduction || '0') +
            parseFloat(item.totalCouponDeduction || '0'),
          avgRevenue: parseFloat(item.avgRevenue || '0'),
        },
        additionalService: {
          orderCount: parseInt(additional.additionalOrderCount || '0'),
          totalRevenue: additionalRev,
          totalOriginalPrice: parseFloat(
            additional.additionalOriginalPrice || '0'
          ),
          totalDiscount:
            parseFloat(additional.additionalCardDeduction || '0') +
            parseFloat(additional.additionalCouponDeduction || '0'),
        },
        totalRevenue,
        totalOrderCount:
          parseInt(item.orderCount) +
          parseInt(additional.additionalOrderCount || '0'),
        avgRevenue:
          totalRevenue > 0 &&
          parseInt(item.orderCount) +
            parseInt(additional.additionalOrderCount || '0') >
            0
            ? (
                totalRevenue /
                (parseInt(item.orderCount) +
                  parseInt(additional.additionalOrderCount || '0'))
              ).toFixed(2)
            : '0.00',
      };
    });

    // 获取总数
    const totalCount = await Order.findAll({
      where: whereCondition,
      attributes: ['employeeId'],
      group: ['employeeId'],
      raw: true,
    });

    return {
      list: formattedStats,
      total: totalCount.length,
      page,
      pageSize,
    };
  }
}
